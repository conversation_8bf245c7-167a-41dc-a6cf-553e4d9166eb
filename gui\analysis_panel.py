"""
Analysis control panel for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List, Callable, Optional

from utils.logging_setup import LoggerMixin
from utils.config import Config

class AnalysisPanel(LoggerMixin):
    """Panel for analysis configuration and control"""
    
    def __init__(self, parent: tk.Widget, config: Config, start_callback: Callable, theme=None):
        self.parent = parent
        self.config = config
        self.start_callback = start_callback
        self.theme = theme
        self.available_models = []
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the analysis control interface"""
        # Analysis type selection
        type_frame = ttk.LabelFrame(self.parent, text="Analysis Type", padding=5)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.analysis_type_var = tk.StringVar(value="iterative")
        
        ttk.Radiobutton(
            type_frame,
            text="Iterative Analysis",
            variable=self.analysis_type_var,
            value="iterative",
            command=self.on_analysis_type_changed
        ).pack(anchor=tk.W)
        
        ttk.Radiobutton(
            type_frame,
            text="Recursive Analysis",
            variable=self.analysis_type_var,
            value="recursive",
            command=self.on_analysis_type_changed
        ).pack(anchor=tk.W)

        ttk.Radiobutton(
            type_frame,
            text="Comparative Analysis",
            variable=self.analysis_type_var,
            value="comparative",
            command=self.on_analysis_type_changed
        ).pack(anchor=tk.W)

        ttk.Radiobutton(
            type_frame,
            text="SWOT Analysis",
            variable=self.analysis_type_var,
            value="swot",
            command=self.on_analysis_type_changed
        ).pack(anchor=tk.W)

        ttk.Radiobutton(
            type_frame,
            text="Temporal Analysis",
            variable=self.analysis_type_var,
            value="temporal",
            command=self.on_analysis_type_changed
        ).pack(anchor=tk.W)
        
        # Analysis description
        self.analysis_desc_var = tk.StringVar()
        self.analysis_desc_label = ttk.Label(
            type_frame,
            textvariable=self.analysis_desc_var,
            foreground="gray",
            wraplength=300
        )
        self.analysis_desc_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Model selection
        model_frame = ttk.LabelFrame(self.parent, text="AI Model", padding=5)
        model_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(model_frame, text="Model:").pack(anchor=tk.W)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(
            model_frame,
            textvariable=self.model_var,
            state="readonly",
            width=30
        )
        self.model_combo.pack(fill=tk.X, pady=(2, 5))
        
        # Model info
        self.model_info_var = tk.StringVar()
        self.model_info_label = ttk.Label(
            model_frame,
            textvariable=self.model_info_var,
            foreground="gray",
            font=('Arial', 8)
        )
        self.model_info_label.pack(anchor=tk.W)
        
        # Analysis parameters
        params_frame = ttk.LabelFrame(self.parent, text="Parameters", padding=5)
        params_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Temperature
        temp_frame = ttk.Frame(params_frame)
        temp_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(temp_frame, text="Temperature:").pack(side=tk.LEFT)
        self.temperature_var = tk.DoubleVar(value=self.config.get("ollama.temperature", 0.7))
        self.temperature_scale = ttk.Scale(
            temp_frame,
            from_=0.0,
            to=2.0,
            variable=self.temperature_var,
            orient=tk.HORIZONTAL,
            length=150
        )
        self.temperature_scale.pack(side=tk.LEFT, padx=(10, 5))
        
        self.temperature_label = ttk.Label(temp_frame, text="0.7")
        self.temperature_label.pack(side=tk.LEFT)
        
        self.temperature_scale.configure(command=self.update_temperature_label)
        
        # Max depth (for recursive analysis)
        depth_frame = ttk.Frame(params_frame)
        depth_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(depth_frame, text="Max Depth:").pack(side=tk.LEFT)
        self.max_depth_var = tk.IntVar(value=self.config.get("analysis.max_recursive_depth", 3))
        self.max_depth_spin = ttk.Spinbox(
            depth_frame,
            from_=1,
            to=10,
            textvariable=self.max_depth_var,
            width=5
        )
        self.max_depth_spin.pack(side=tk.LEFT, padx=(10, 0))
        
        # Connection threshold
        conn_frame = ttk.Frame(params_frame)
        conn_frame.pack(fill=tk.X)
        
        ttk.Label(conn_frame, text="Connection Threshold:").pack(side=tk.LEFT)
        self.connection_threshold_var = tk.DoubleVar(
            value=self.config.get("analysis.connection_threshold", 0.7)
        )
        self.connection_scale = ttk.Scale(
            conn_frame,
            from_=0.0,
            to=1.0,
            variable=self.connection_threshold_var,
            orient=tk.HORIZONTAL,
            length=100
        )
        self.connection_scale.pack(side=tk.LEFT, padx=(10, 5))
        
        self.connection_label = ttk.Label(conn_frame, text="0.7")
        self.connection_label.pack(side=tk.LEFT)
        
        self.connection_scale.configure(command=self.update_connection_label)
        
        # Advanced options
        advanced_frame = ttk.LabelFrame(self.parent, text="Advanced Options", padding=5)
        advanced_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.enable_caching_var = tk.BooleanVar(
            value=self.config.get("analysis.enable_caching", True)
        )
        ttk.Checkbutton(
            advanced_frame,
            text="Enable result caching",
            variable=self.enable_caching_var
        ).pack(anchor=tk.W)
        
        self.parallel_processing_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            advanced_frame,
            text="Parallel processing (experimental)",
            variable=self.parallel_processing_var
        ).pack(anchor=tk.W)
        
        # Control buttons
        control_frame = ttk.Frame(self.parent)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.start_button = ttk.Button(
            control_frame,
            text="Start Analysis",
            command=self.start_analysis,
            style="Accent.TButton"
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.reset_button = ttk.Button(
            control_frame,
            text="Reset",
            command=self.reset
        )
        self.reset_button.pack(side=tk.LEFT)
        
        # Initialize UI state
        self.on_analysis_type_changed()
        self.update_temperature_label()
        self.update_connection_label()
    
    def on_analysis_type_changed(self):
        """Handle analysis type change"""
        analysis_type = self.analysis_type_var.get()
        
        descriptions = {
            "iterative": "Analyze each sub-topic sequentially, then find connections between them.",
            "recursive": "Start with main topic and recursively generate and analyze sub-topics to specified depth.",
            "comparative": "Compare and contrast multiple topics with scoring and recommendations.",
            "swot": "Structured analysis of Strengths, Weaknesses, Opportunities, and Threats.",
            "temporal": "Analyze topics across time dimensions with trends and predictions."
        }
        
        self.analysis_desc_var.set(descriptions.get(analysis_type, ""))
        
        # Enable/disable depth control based on analysis type
        if analysis_type == "recursive":
            self.max_depth_spin.configure(state="normal")
        else:
            self.max_depth_spin.configure(state="disabled")
    
    def update_temperature_label(self, value=None):
        """Update temperature label"""
        temp = self.temperature_var.get()
        self.temperature_label.configure(text=f"{temp:.2f}")
    
    def update_connection_label(self, value=None):
        """Update connection threshold label"""
        threshold = self.connection_threshold_var.get()
        self.connection_label.configure(text=f"{threshold:.2f}")
    
    def update_models(self, models: List[Dict[str, Any]]):
        """Update available models list"""
        self.available_models = models
        model_names = [model.get("name", "Unknown") for model in models]
        
        self.model_combo['values'] = model_names
        
        # Set default model
        default_model = self.config.get("ollama.default_model", "llama2")
        if default_model in model_names:
            self.model_var.set(default_model)
        elif model_names:
            self.model_var.set(model_names[0])
        
        self.update_model_info()
        
        # Bind model selection change
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_changed)
    
    def on_model_changed(self, event=None):
        """Handle model selection change"""
        self.update_model_info()
    
    def update_model_info(self):
        """Update model information display"""
        selected_model = self.model_var.get()
        
        # Find model info
        model_info = None
        for model in self.available_models:
            if model.get("name") == selected_model:
                model_info = model
                break
        
        if model_info:
            size = model_info.get("size", 0)
            size_mb = size / (1024 * 1024) if size else 0
            modified = model_info.get("modified_at", "Unknown")
            
            info_text = f"Size: {size_mb:.1f} MB"
            if modified != "Unknown":
                info_text += f" | Modified: {modified[:10]}"
            
            self.model_info_var.set(info_text)
        else:
            self.model_info_var.set("Model information not available")
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """Get current analysis configuration"""
        return {
            "type": self.analysis_type_var.get(),
            "model": self.model_var.get(),
            "temperature": self.temperature_var.get(),
            "max_depth": self.max_depth_var.get(),
            "connection_threshold": self.connection_threshold_var.get(),
            "enable_caching": self.enable_caching_var.get(),
            "parallel_processing": self.parallel_processing_var.get()
        }
    
    def start_analysis(self):
        """Start analysis with current configuration"""
        if not self.model_var.get():
            tk.messagebox.showerror("No Model", "Please select an AI model first.")
            return
        
        config = self.get_analysis_config()
        self.start_callback(config)
    
    def reset(self):
        """Reset analysis parameters to defaults"""
        self.analysis_type_var.set("iterative")
        self.temperature_var.set(self.config.get("ollama.temperature", 0.7))
        self.max_depth_var.set(self.config.get("analysis.max_recursive_depth", 3))
        self.connection_threshold_var.set(self.config.get("analysis.connection_threshold", 0.7))
        self.enable_caching_var.set(self.config.get("analysis.enable_caching", True))
        self.parallel_processing_var.set(False)
        
        # Update UI
        self.on_analysis_type_changed()
        self.update_temperature_label()
        self.update_connection_label()
    
    def set_analysis_running(self, running: bool):
        """Update UI based on analysis running state"""
        state = "disabled" if running else "normal"
        
        self.start_button.configure(state=state)
        self.model_combo.configure(state="disabled" if running else "readonly")
        self.temperature_scale.configure(state=state)
        self.max_depth_spin.configure(state=state)
        self.connection_scale.configure(state=state)
        
        if running:
            self.start_button.configure(text="Analysis Running...")
        else:
            self.start_button.configure(text="Start Analysis")
    
    def cleanup(self):
        """Cleanup resources"""
        pass
